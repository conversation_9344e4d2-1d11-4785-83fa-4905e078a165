# -*- coding: utf-8 -*-
"""
Read local SHP and add DEM data
1. Plot SHP and South China Sea inset map
2. Plot site data
3. Add DEM data
4. Add elevation statistics for latitude (right side) and longitude (top side)

#配色方案 #E0B77F #BABABC #B7C685 #6A8864 #629641
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as font_manager
import cartopy.crs as ccrs
import cartopy.io.shapereader as shpreader
import cartopy.feature as cfeature
import geopandas as gpd
import matplotlib.patches as mpatches
import rasterio
from rasterio.plot import reshape_as_image
from matplotlib.colors import Normalize, ListedColormap
from matplotlib.cm import ScalarMappable
import numpy as np
from matplotlib.lines import Line2D
import os
from matplotlib.transforms import Affine2D


# Custom function to load .rgb colormap and skip invalid lines
def load_rgb_colormap(file_path):
    colors = []
    with open(file_path, 'r') as file:
        for line in file:
            # Skip lines that are comments or non-RGB data (e.g., "ncolors")
            if line.startswith('#') or not line.strip():
                continue  # Skip comments or empty lines
            
            # Split the line into components
            try:
                rgb = [int(x) for x in line.strip().split()]
                if len(rgb) == 3:  # Ensure it's a valid RGB line
                    colors.append([rgb[0]/255, rgb[1]/255, rgb[2]/255])  # Normalize to [0, 1] range
            except ValueError:
                # Skip lines that can't be converted to integers
                continue

    return ListedColormap(colors)


# Set local data paths
fig = plt.figure(figsize=(12, 8), dpi=300)

Global_countries_path = r'E:/SHP/WGS_1984_SHP/global_countries.shp'
China_path = r'E:/SHP/WGS_1984_SHP/China.shp'
China_line_path = r'E:/SHP/WGS_1984_SHP/合并_中国_省_MultiLineString.shp'
arid_area = r'E:/SHP/WGS_1984_SHP/合并后的干旱区.shp'
semi_arid_area = r'E:/SHP/WGS_1984_SHP/合并后的半干旱区.shp'
semi_humid_area = r'E:/SHP/WGS_1984_SHP/合并后的半湿润地区.shp'
humid_area = r'E:/SHP/WGS_1984_SHP/合并后的湿润地区.shp'
semi_humid_area_2 = r'E:/SHP/WGS_1984_SHP/湿润_半湿润地区.shp'
sites = r'E:/SHP/WGS_1984_SHP/China_point.shp'
dem_path = r'E:/SHP/China_dem_reprojected.tif'

# Set global font to Times New Roman, font size to 12
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# Load custom colormap
cmap_path = r'E:/色带/python色带/cmaps-master/cmaps-master/cmaps/colormaps/ncar_ncl/cmp_b2r.rgb'  
#cmp_b2r.rgb
#amwg256.rgb #BlueRed.rgb #Cat12.rgb #hotcolr_19lev.rgb #matlab_hot.rgb #MPL_afmhot.rgb #MPL_RdPu.rgb 
#BlAqGrYeOrReVi200.rgb #BlueWhiteOrangeRed.rgb #cb_rainbow.rgb #CBR_wet.rgb #amwg.rgb
custom_cmap = load_rgb_colormap(cmap_path)

# Create a map with PlateCarree projection (main map)
ax_main = plt.axes([0.15, 0.2, 0.6, 0.6], projection=ccrs.PlateCarree())  # Adjust main map position

# ======================= Read and plot DEM data ========================
with rasterio.open(dem_path) as dem:
    dem_data = dem.read(1)
    dem_data = dem_data.astype(float)
    dem_data[dem_data == -32766] = np.nan
    dem_data[dem_data < -500] = np.nan
    dem_data = np.ma.masked_invalid(dem_data)

    # Get latitude and longitude bounds
    dem_bounds = dem.bounds
    extent = [dem_bounds.left, dem_bounds.right, dem_bounds.bottom, dem_bounds.top]

    # Set normalization for DEM
    norm = Normalize(vmin=np.nanmin(dem_data), vmax=np.nanmax(dem_data))

    # Set NaN values to transparent
    custom_cmap.set_bad(color='white', alpha=0.0)

    # Plot DEM data with custom colormap
    dem_plot = ax_main.imshow(dem_data, origin='upper', cmap=custom_cmap,
                              extent=extent, transform=ccrs.PlateCarree(), alpha=0.95, zorder=0, norm=norm)

    # Add colorbar
    cbar = plt.colorbar(ScalarMappable(norm=norm, cmap=custom_cmap), 
                        ax=ax_main, orientation='horizontal', fraction=0.075, pad=0.05, label='Elevation (m)')
    cbar.ax.tick_params(direction='in')

# ===================== Add elevation statistics ========================
# Plot latitude and longitude profiles (same as original code)
# Latitude profile (Right-side subplot)
ax_lat = plt.axes([0.77, 0.275, 0.15, 0.52], sharey=ax_main)  # 调整位置和高度，使副图与主图对齐，避开色带

# 创建纬度数组，与DEM数据行数对应
latitudes = np.linspace(extent[2], extent[3], dem_data.shape[0])

# 确保纬度顺序正确，纬度应从 18°N 到 54°N
if latitudes[0] < latitudes[-1]:
    latitudes = latitudes[::-1]  # 如果顺序是从北到南，则翻转为从南到北

# 如果纬度顺序是从北到南，还需要将 dem_data 按行翻转
if latitudes[0] < latitudes[-1]:
    dem_data = np.flipud(dem_data)  # 上下翻转 DEM 数据，使其纬度顺序从南到北

# 计算纬度方向的平均海拔、最低海拔和最高海拔
elevations_lat = np.nanmean(dem_data, axis=1)  # 计算纬度方向的平均海拔
min_elev_lat = np.nanmin(dem_data, axis=1)     # 计算纬度方向的最低海拔
max_elev_lat = np.nanmax(dem_data, axis=1)     # 计算纬度方向的最高海拔

# 修剪纬度数组和对应的数据，以确保只绘制 18°N 到 54°N 的范围
latitudes_filtered = latitudes[(latitudes >= 18) & (latitudes <= 54)]
elevations_lat_filtered = elevations_lat[(latitudes >= 18) & (latitudes <= 54)]
min_elev_lat_filtered = min_elev_lat[(latitudes >= 18) & (latitudes <= 54)]
max_elev_lat_filtered = max_elev_lat[(latitudes >= 18) & (latitudes <= 54)]

# 绘制纬度-海拔统计图，确保显示有效纬度范围
ax_lat.plot(elevations_lat_filtered, latitudes_filtered, color='blue', label='Mean Elevation')  # 绘制平均海拔曲线
ax_lat.fill_betweenx(latitudes_filtered, min_elev_lat_filtered, max_elev_lat_filtered, color='#AFC8E2', alpha=0.65, label='Elevation range')  # '#AFC8E2'填充海拔范围

# 设置x轴为海拔，y轴为共享的纬度，并且设置纬度范围为 [18, 54]，与主图对齐
ax_lat.set_xlabel('Elevation (m)')
ax_lat.set_ylim([18, 54])  # 设置与主图相同的纬度范围
ax_lat.grid(True, linestyle='--' , color='#AFC8E2', alpha=0.65)  # #AFC8E2设置虚线的网格线
ax_lat.grid(True)

# 调整x轴范围，确保海拔最大值和最小值完全显示
ax_lat.set_xlim(np.nanmin(min_elev_lat_filtered) - 100, np.nanmax(max_elev_lat_filtered) + 200)

# 如果纬度顺序是反的，确保y轴从南到北（从18°N到54°N）
if latitudes_filtered[0] < latitudes_filtered[-1]:
    ax_lat.invert_yaxis()  # 反转y轴，以便从南到北绘制

# 将纵坐标刻度放在右侧，并设置刻度朝内
ax_lat.yaxis.tick_right()  # 将刻度放置在右侧
ax_lat.tick_params(axis='y', direction='in')  # 刻度朝内

# 设置纬度标签格式为 '°N'
ax_lat.set_yticklabels([f'{int(lat)}°N' for lat in ax_lat.get_yticks()])

# 添加图例
legend = ax_lat.legend(loc='upper right', framealpha=0, ncol=1, bbox_to_anchor=(1, 1) ,fontsize=8)
# 对图例进行 90 度旋转
legend.set_transform(legend.get_transform() + Affine2D().rotate_deg(90))

# Longitude profile (Top-side subplot)
ax_lon = plt.axes([0.15, 0.81, 0.6, 0.15], sharex=ax_main)
longitudes = np.linspace(extent[0], extent[1], dem_data.shape[1])
elevations_lon = np.nanmean(dem_data, axis=0)
min_elev_lon = np.nanmin(dem_data, axis=0)
max_elev_lon = np.nanmax(dem_data, axis=0)

ax_lon.plot(longitudes, elevations_lon, color='blue', label='Mean elevation')
ax_lon.fill_between(longitudes, min_elev_lon, max_elev_lon, color='#AFC8E2', alpha=0.65, label='Elevation range')
ax_lon.set_ylabel('Elevation (m)')
ax_lon.grid(True, linestyle='--', color='#AFC8E2', alpha=0.9)
ax_lon.set_ylim(np.nanmin(min_elev_lon) - 100, np.nanmax(max_elev_lon) + 200)
ax_lon.tick_params(axis='both', direction='in')
ax_lon.xaxis.set_label_position('top')
ax_lon.xaxis.tick_top()
ax_lon.tick_params(labelbottom=False)
ax_lon.set_xticklabels([f'{int(lon)}°E' for lon in ax_lon.get_xticks()])
ax_lon.legend(loc='upper right', framealpha=0)

# ================== Add map features and site data ==================
# Define colors and transparency for various regions

China_feature = cfeature.ShapelyFeature(shpreader.Reader(China_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=1, zorder=2)

Global_countries_feature = cfeature.ShapelyFeature(shpreader.Reader(Global_countries_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='grey', facecolor='none', linewidth=0.5, alpha=0.5, zorder=1)

China_line_feature = cfeature.ShapelyFeature(shpreader.Reader(China_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2, zorder=2)

arid_feature = cfeature.ShapelyFeature(shpreader.Reader(arid_area).geometries(),
                                       ccrs.PlateCarree(), edgecolor='#E0B77F', facecolor='none', linewidth=1, alpha=0.9, zorder=1)

semi_arid_feature = cfeature.ShapelyFeature(shpreader.Reader(semi_arid_area).geometries(),
                                            ccrs.PlateCarree(), edgecolor='#BABABC', facecolor='none', linewidth=1, alpha=0.9, zorder=1)

humid_feature = cfeature.ShapelyFeature(shpreader.Reader(humid_area).geometries(),
                                        ccrs.PlateCarree(), edgecolor='#629641', facecolor='none', linewidth=1, alpha=0.9, zorder=1)

semi_humid_feature = cfeature.ShapelyFeature(shpreader.Reader(semi_humid_area).geometries(),
                                             ccrs.PlateCarree(), edgecolor='#6A8864', facecolor='none', linewidth=1, alpha=0.9, zorder=1)

semi_humid_area_2_feature = cfeature.ShapelyFeature(shpreader.Reader(semi_humid_area_2).geometries(),
                                                    ccrs.PlateCarree(), edgecolor='#B7C685', facecolor='none', linewidth=1, alpha=0.9, zorder=1)
#'#E0B77F','#BABABC','#629641','#6A8864','#B7C685',
ax_main.add_feature(arid_feature)
ax_main.add_feature(semi_arid_feature)
ax_main.add_feature(humid_feature)
ax_main.add_feature(semi_humid_feature)
ax_main.add_feature(semi_humid_area_2_feature)
ax_main.add_feature(Global_countries_feature)
ax_main.add_feature(China_feature)
ax_main.add_feature(China_line_feature)
# Plot site data
sites_df = gpd.read_file(sites)
ax_main.scatter(sites_df.geometry.x, sites_df.geometry.y, color='black', s=8, label='Sites', alpha=1, zorder=3)
ax_main.set_extent([73, 135, 18, 54], crs=ccrs.PlateCarree())

# Add gridlines
gl = ax_main.gridlines(draw_labels=True, linewidth=0.3, color='grey', alpha=0.6, linestyle='--')
gl.xlabel_style = {'size': 12, 'color': 'black', 'fontname': 'Times New Roman'}
gl.ylabel_style = {'size': 12, 'color': 'black', 'fontname': 'Times New Roman'}
gl.top_labels = False
gl.right_labels = False

# ======================= Plot South China Sea inset map ========================
ax_inset = plt.axes([0.595, 0.285, 0.2, 0.2], projection=ccrs.PlateCarree())
China_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=1)
China_line_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2)
ax_inset.set_extent([106, 125, 2, 28], crs=ccrs.PlateCarree())
ax_inset.add_feature(China_feature_inset)
ax_inset.add_feature(China_line_feature_inset)
ax_inset.set_xticks([])
ax_inset.set_yticks([])
#===================================================================================

#配色方案 #E0B77F #BABABC #B7C685 #6A8864 #629641 
#'#E0B77F','#BABABC','#629641','#6A8864','#B7C685',
# ======================= Add legend to the main map ========================
arid_patch = mpatches.Patch(edgecolor='#E0B77F', facecolor='none', label='Arid Area', linewidth=1, alpha=0.7)
semi_arid_patch = mpatches.Patch(edgecolor='#BABABC', facecolor='none', label='Semi-Arid Area', linewidth=1, alpha=0.7)
humid_patch = mpatches.Patch(edgecolor='#629641', facecolor='none', label='Humid Area', linewidth=1, alpha=0.7)
semi_humid_patch = mpatches.Patch(edgecolor='#6A8864', facecolor='none', label='Semi-Humid Area', linewidth=1, alpha=0.7)
semi_humid_area_2_patch = mpatches.Patch(edgecolor='#B7C685', facecolor='none', label='Humid/Semi-Humid Area', linewidth=1, alpha=0.7)
site_legend = Line2D([0], [0], marker='o', color='black', label='Meteorological Stations', markersize=6, linestyle='None')

ax_main.legend(handles=[arid_patch, semi_arid_patch, humid_patch, semi_humid_patch, semi_humid_area_2_patch, site_legend],
               loc='lower left', bbox_to_anchor=(0.005, 0.015), fancybox=False, shadow=False, fontsize=10, framealpha=0)

# ======================= Finished plotting ========================

# ======================= Plot bar chart for station counts ========================
# Calculate number of stations in each region
# 定义缩写映射关系
short_names = {
    'Arid': 'Ar.',
    'Semi-Arid': 'SA.',
    'Humid': 'Hum.',
    'Semi-Humid': 'SH.',
    'Humid/Semi-Humid': 'H/SH.'
}

# 计算站点数量并应用缩写
def count_stations_in_region(stations_df, region_shapefile):
    # 读取区域 shapefile 并确保坐标系一致
    region_gdf = gpd.read_file(region_shapefile)
    
    # 检查并匹配坐标系，如果不同则统一为 EPSG:4326
    if stations_df.crs != region_gdf.crs:
        stations_df = stations_df.to_crs(region_gdf.crs)
    
    # 使用 unary_union 合并区域多边形，确保判断时没有分割的几何
    merged_region = region_gdf.unary_union
    
    # 使用 within() 判断站点是否在区域内
    return stations_df.within(merged_region).sum()

# 站点数据
sites_df = gpd.read_file(sites)

# 计算站点数量并应用缩写
station_counts = {
    short_names['Arid']: count_stations_in_region(sites_df, arid_area),
    short_names['Semi-Arid']: count_stations_in_region(sites_df, semi_arid_area),
    short_names['Humid']: count_stations_in_region(sites_df, humid_area),
    short_names['Semi-Humid']: count_stations_in_region(sites_df, semi_humid_area),
    short_names['Humid/Semi-Humid']: count_stations_in_region(sites_df, semi_humid_area_2)
}

# 打印站点数量，检查是否计算正确
print(station_counts)

# Create a bar chart in the top right corner
ax_bar = plt.axes([0.77, 0.82, 0.15, 0.15])  # Adjust position and size of the bar chart
bars = ax_bar.bar(station_counts.keys(), station_counts.values(), color=['#E0B77F','#BABABC','#629641','#6A8864','#B7C685'])

# 设置字体大小小一些
ax_bar.tick_params(axis='x', labelsize=8)
ax_bar.tick_params(axis='y', labelsize=8)

# 将纵坐标刻度移动到右边
ax_bar.yaxis.tick_right()

# 添加每个柱状图上方的站点数量
for bar in bars:
    height = bar.get_height()
    ax_bar.text(bar.get_x() + bar.get_width()/2, height, f'{int(height)}', 
                ha='center', va='bottom', fontsize=8)

# Add labels and grid
ax_bar.set_ylabel('Number of Stations', fontsize=8)
ax_bar.set_xticklabels(station_counts.keys(), rotation=0, ha='center', fontsize=8)
ax_bar.grid(True, linestyle='--', alpha=0.35)

# ========================================================================================================
import matplotlib.patches as mpatches

def add_simple_north_arrow(ax, location=(0.9, 0.9), size=0.1, arrow_color='black', font_color='black'):
    """
    在地图上添加一个简洁的指北针
    :param ax: matplotlib axes
    :param location: 指北针的位置 (x, y) in axis coordinates (比例, [0,1])
    :param size: 指北针大小
    :param arrow_color: 箭头颜色
    :param font_color: 文字颜色
    """
    # 绘制箭头
    ax.annotate('', xy=(location[0], location[1] + size * 0.7), xytext=location,
                arrowprops=dict(facecolor=arrow_color, edgecolor=arrow_color, lw=2, headwidth=10, headlength=15),
                ha='center', va='center', xycoords='axes fraction', zorder=6)

    # 添加 "N" 标记
    ax.text(location[0], location[1] + size * 1.1, 'N', ha='center', va='center', fontsize=16, fontweight='bold',
            transform=ax.transAxes, color=font_color, zorder=6)

# 调用函数，给地图添加新的简洁指北针样式
add_simple_north_arrow(ax_main, location=(0.95, 0.9), size=0.05, arrow_color='black', font_color='black')

# =========================================================================================================

# ======================= 添加比例尺 ========================
from pyproj import Geod
import matplotlib.patches as mpatches

def add_scalebar(ax, length=800, units='km', location=(0.1, 0.02), linewidth=2, color='black', font_color='black'):
    """
    在地图上添加比例尺，长度为真实的地理距离。
    :param ax: matplotlib axes
    :param length: 比例尺的长度（单位：km）
    :param units: 比例尺单位
    :param location: 比例尺放置位置，axes坐标系中的(x, y)坐标
    :param linewidth: 比例尺线条宽度
    :param color: 比例尺颜色
    :param font_color: 文字颜色
    """
    # 获取当前地图的投影信息
    proj = ccrs.PlateCarree()

    # 获取地图左下角和右下角的经纬度（以绘制比例尺）
    extent = ax.get_extent(crs=proj)
    left_lon = extent[0] + (extent[1] - extent[0]) * location[0]  # 左下角经度
    bottom_lat = extent[2] + (extent[3] - extent[2]) * location[1]  # 左下角纬度
    
    # 使用 pyproj.Geod 来计算比例尺的终点经度
    geod = Geod(ellps='WGS84')
    lon2, lat2, _ = geod.fwd(left_lon, bottom_lat, 90, length * 1000)  # 指定长度对应的终点
    
    # 将比例尺在地图上显示出来
    ax.plot([left_lon, lon2], [bottom_lat, bottom_lat], transform=proj, color=color, lw=linewidth, zorder=4)

    # 绘制分段的比例尺
    num_sections = 4
    section_length = length / num_sections
    for i in range(num_sections):
        bar_color = color if i % 2 == 0 else 'white'
        section_lon, section_lat, _ = geod.fwd(left_lon, bottom_lat, 90, section_length * 1000 * i)
        next_section_lon, next_section_lat, _ = geod.fwd(left_lon, bottom_lat, 90, section_length * 1000 * (i + 1))
        ax.add_patch(mpatches.Rectangle(
            (section_lon, bottom_lat), next_section_lon - section_lon, (extent[3] - extent[2]) * 0.01,
            transform=proj, color=bar_color, zorder=4, linewidth=0))

    # 添加比例尺文字
    ax.text((left_lon + lon2) / 2, bottom_lat - (extent[3] - extent[2]) * 0.03,
            f'{length} {units}', ha='center', va='top', fontsize=10, color=font_color,
            transform=proj, zorder=5)


# 调用函数，给地图添加比例尺
add_scalebar(ax_main, length=800, units='km', location=(0.3, 0.07), linewidth=1.5, color='black', font_color='black')

# ======================= Finished plotting ========================

plt.rcParams['axes.unicode_minus'] = False

# 保存图片为 PNG、SVG 和 PDF 格式
plt.savefig(r'E:\extreme_snowfall\China_2169\中国图\01图\China_map.png', format='png', dpi=300, transparent=True)
plt.savefig(r'E:\extreme_snowfall\China_2169\中国图\01图\China_map.svg', format='svg', transparent=True)
plt.savefig(r'E:\extreme_snowfall\China_2169\中国图\01图\China_map.pdf', format='pdf', dpi=300, transparent=True)
plt.show()
