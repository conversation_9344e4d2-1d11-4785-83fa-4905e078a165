import rasterio
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import geopandas as gpd
import numpy as np

# 创建地图，使用 Orthographic 投影实现圆球形效果
fig = plt.figure(figsize=(12, 12), dpi=300)  # 调整图像大小
ax_main = plt.axes(projection=ccrs.Orthographic(central_longitude=105, central_latitude=20))  #35 使用正交投影，设置中心经纬度

# 文件路径
dem_path = r'plot\研究区示意图-分享-lyq\标准地图_ETOPO2022_配色1.tif'
Global_countries_path = r'plot\研究区示意图-分享-lyq\WGS_1984_SHP\global_countries.shp'
China_path = r'plot\研究区示意图-分享-lyq\WGS_1984_SHP\China.shp'
China_line_path = r'plot\研究区示意图-分享-lyq\WGS_1984_SHP\合并_中国_省_MultiLineString.shp'
tianshan_path = r'plot\研究区示意图-分享-lyq\WGS_1984_SHP\Ts_mountain_C.shp'

# 读取 .tif 文件中的三个波段
with rasterio.open(dem_path) as src:
    band1 = src.read(1)  # 红色通道
    band2 = src.read(2)  # 绿色通道
    band3 = src.read(3)  # 蓝色通道
    extent = [src.bounds.left, src.bounds.right, src.bounds.bottom, src.bounds.top]

    # 归一化波段到 [0, 1] 范围
    band1_normalized = (band1 - band1.min()) / (band1.max() - band1.min())
    band2_normalized = (band2 - band2.min()) / (band2.max() - band2.min())
    band3_normalized = (band3 - band3.min()) / (band3.max() - band3.min())

    # 将三个波段堆叠为 RGB 图像
    rgb_image = np.dstack((band1_normalized, band2_normalized, band3_normalized))

    # 绘制 DEM 图像作为底图
    ax_main.imshow(rgb_image, origin='upper', extent=extent, transform=ccrs.PlateCarree(), zorder=1)

# 添加不同区域的特征到地图
China_feature = cfeature.ShapelyFeature(gpd.read_file(China_path).geometry, ccrs.PlateCarree(), 
                                        edgecolor='red', facecolor='none', linewidth=2, zorder=3)
Global_countries_feature = cfeature.ShapelyFeature(gpd.read_file(Global_countries_path).geometry, ccrs.PlateCarree(), 
                                                   edgecolor='grey', facecolor='none', linewidth=0.5, alpha=0.5, zorder=3)
China_line_feature = cfeature.ShapelyFeature(gpd.read_file(China_line_path).geometry, ccrs.PlateCarree(),  
                                             edgecolor='red', facecolor='none', linewidth=2, zorder=3)
tianshan_feature = cfeature.ShapelyFeature(gpd.read_file(tianshan_path).geometry, ccrs.PlateCarree(),  
                                             edgecolor='red', facecolor='#2ca02c', linewidth=2, zorder=4)

# 添加地图特征（添加天山特征）
ax_main.add_feature(China_line_feature)
ax_main.add_feature(Global_countries_feature)
ax_main.add_feature(China_feature)
ax_main.add_feature(tianshan_feature)  # 添加天山特征

# 在右上角添加 "(a)" 标注
ax_main.text(0.005, 0.98, '(a)', transform=ax_main.transAxes, fontsize=40, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold')

# 添加网格线并显示经纬度标签
gl = ax_main.gridlines(draw_labels=True, linewidth=0.3, color='grey', alpha=0.6, linestyle='--')

# 设置经纬度标签格式
gl.xlabel_style = {'size': 30, 'color': 'black', 'fontname': 'Times New Roman'}
gl.ylabel_style = {'size': 30, 'color': 'black', 'fontname': 'Times New Roman'}

# 保存图片为 PNG、SVG 和 PDF 格式
plt.savefig(r'plot\研究区示意图-分享-lyq\output\map_output211.png', format='png', dpi=300, transparent=True, pad_inches=0.001)
plt.savefig(r'plot\研究区示意图-分享-lyq\output\map_output211.svg', format='svg', dpi=300, transparent=True, pad_inches=0.001)
plt.savefig(r'plot\研究区示意图-分享-lyq\output\map_output211.pdf', format='pdf', dpi=300, transparent=True, pad_inches=0.001)

# 显示图像
plt.show()
