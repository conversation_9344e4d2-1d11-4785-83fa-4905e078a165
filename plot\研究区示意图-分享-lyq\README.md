# 研究区示意图绘制程序

## 概述
这个程序用于绘制全球和中国的研究区示意图，包含DEM数据、国家边界、省界和山脉信息。

## 修复内容
1. **文件路径修复**: 将不存在的 `Ts_mountain_C.shp` 替换为 `全国主要山脉EN.shp`
2. **错误处理**: 添加了完整的错误处理机制，确保程序在某些文件缺失时仍能运行
3. **目录创建**: 自动创建输出目录
4. **文件检查**: 在运行前检查所有必需文件是否存在

## 系统要求
- Python 3.7+
- 所需的Python库（见requirements.txt）

## 安装步骤

### 1. 安装Python
如果系统中没有Python，请先安装Python：
- 访问 https://www.python.org/downloads/
- 下载并安装最新版本的Python
- 确保在安装时勾选"Add Python to PATH"

### 2. 安装依赖库
```bash
pip install -r requirements.txt
```

或者单独安装：
```bash
pip install rasterio matplotlib cartopy geopandas numpy
```

### 3. 检查依赖
运行依赖检查脚本：
```bash
python check_dependencies.py
```

## 运行程序

### 方法1: 直接运行
```bash
python "001全球+中国shp.py"
```

### 方法2: 使用Python解释器
```bash
python -m "001全球+中国shp"
```

## 输出文件
程序将在 `output/` 目录下生成以下文件：
- `map_output211.png` - PNG格式图像
- `map_output211.svg` - SVG格式图像  
- `map_output211.pdf` - PDF格式图像

## 故障排除

### 常见问题
1. **Python未找到**: 确保Python已正确安装并添加到系统PATH
2. **依赖库缺失**: 运行 `check_dependencies.py` 检查并安装缺失的库
3. **文件路径错误**: 确保所有数据文件都在正确的位置

### 文件结构
```
plot/研究区示意图-分享-lyq/
├── 001全球+中国shp.py          # 主程序
├── check_dependencies.py       # 依赖检查脚本
├── requirements.txt            # 依赖列表
├── README.md                  # 说明文档
├── 标准地图_ETOPO2022_配色1.tif # DEM数据
├── WGS_1984_SHP/              # Shapefile数据目录
│   ├── global_countries.shp   # 全球国家边界
│   ├── China.shp              # 中国边界
│   ├── 合并_中国_省_MultiLineString.shp # 中国省界
│   └── 全国主要山脉EN.shp      # 山脉数据
└── output/                    # 输出目录（自动创建）
```

## 注意事项
- 程序需要较大的内存来处理DEM数据
- 首次运行可能需要下载cartopy的地图数据
- 确保有足够的磁盘空间存储输出文件
