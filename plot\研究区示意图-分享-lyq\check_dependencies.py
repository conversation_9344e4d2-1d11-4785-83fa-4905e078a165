#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所需依赖库是否已安装
"""

import sys

def check_dependencies():
    """检查所需的Python库是否已安装"""
    required_packages = [
        'rasterio',
        'matplotlib',
        'cartopy',
        'geopandas', 
        'numpy',
        'os'  # 内置库
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'os':
                import os
                print(f"✓ {package} - 已安装 (内置库)")
            else:
                __import__(package)
                print(f"✓ {package} - 已安装")
        except ImportError:
            print(f"✗ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖库: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n所有依赖库都已安装！")
        return True

if __name__ == "__main__":
    print("检查Python依赖库...")
    print(f"Python版本: {sys.version}")
    print("-" * 50)
    
    if check_dependencies():
        print("可以运行主程序了！")
    else:
        print("请先安装缺少的依赖库。")
