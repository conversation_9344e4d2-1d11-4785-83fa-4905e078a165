# -*- coding: utf-8 -*-
"""
Read local SHP and add DEM data
1. Plot SHP and South China Sea inset map
2. Plot site data
3. Add DEM data
4. Add elevation statistics for latitude (right side) and longitude (top side)

#配色方案 #E0B77F #BABABC #B7C685 #6A8864 #629641
"""
import matplotlib.lines as mlines
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.io.shapereader as shpreader
import cartopy.feature as cfeature
import geopandas as gpd
import rasterio
from matplotlib.colors import Normalize, ListedColormap
from matplotlib.cm import ScalarMappable
import numpy as np
from shapely.geometry import LineString #, Polygon
import math
import matplotlib.patches as mpatches
import os

# Custom function to load .rgb colormap and skip invalid lines
def load_rgb_colormap(file_path):
    colors = []
    with open(file_path, 'r') as file:
        for line in file:
            # Skip lines that are comments or non-RGB data (e.g., "ncolors")
            if line.startswith('#') or not line.strip():
                continue  # Skip comments or empty lines
            
            # Split the line into components
            try:
                rgb = [int(x) for x in line.strip().split()]
                if len(rgb) == 3:  # Ensure it's a valid RGB line
                    colors.append([rgb[0]/255, rgb[1]/255, rgb[2]/255])  # Normalize to [0, 1] range
            except ValueError:
                # Skip lines that can't be converted to integers
                continue

    return ListedColormap(colors)


# Set local data paths
fig = plt.figure(figsize=(12, 8), dpi=500)

Global_countries_path = r'E:/SHP/WGS_1984_SHP/global_countries.shp'
China_path = r'E:/SHP/WGS_1984_SHP/China.shp'
China_line_path = r'E:/SHP/WGS_1984_SHP/合并_中国_省_MultiLineString.shp'
arid_area = r'E:/SHP/WGS_1984_SHP/合并后的干旱区.shp'
semi_arid_area = r'E:/SHP/WGS_1984_SHP/合并后的半干旱区.shp'
semi_humid_area = r'E:/SHP/WGS_1984_SHP/合并后的半湿润地区.shp'
humid_area = r'E:/SHP/WGS_1984_SHP/合并后的湿润地区.shp'
semi_humid_area_2 = r'E:/SHP/WGS_1984_SHP/湿润_半湿润地区.shp'
sites = r'E:/SHP/WGS_1984_SHP/China_point.shp'
dem_path = r'E:/SHP/China_dem_reprojected.tif'
mountains_path = r'E:/SHP/WGS_1984_SHP/全国主要山脉EN.shp' 
river_line_path = r'E:/SHP/WGS_1984_SHP/hyd1_4l.shp'
river_p_path = r'E:/SHP/WGS_1984_SHP/hyd1_4p.shp'

# Set global font to Times New Roman, font size to 12
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 16

# Load custom colormap
cmap_path = r'E:/色带/python色带/cmaps-master/cmaps-master/cmaps/colormaps/ncar_ncl/cmp_b2r.rgb'  
#cmp_b2r.rgb
#amwg256.rgb #BlueRed.rgb #Cat12.rgb #hotcolr_19lev.rgb #matlab_hot.rgb #MPL_afmhot.rgb #MPL_RdPu.rgb 
#BlAqGrYeOrReVi200.rgb #BlueWhiteOrangeRed.rgb #cb_rainbow.rgb #CBR_wet.rgb #amwg.rgb
custom_cmap = load_rgb_colormap(cmap_path)

# Create a map with PlateCarree projection (main map)
ax_main = plt.axes([0.15, 0.2, 0.6, 0.6], projection=ccrs.PlateCarree())  # Adjust main map position

# ======================= Read and plot DEM data ========================
with rasterio.open(dem_path) as dem:
    dem_data = dem.read(1)
    dem_data = dem_data.astype(float)
    dem_data[dem_data == -32766] = np.nan
    dem_data[dem_data < -500] = np.nan
    dem_data = np.ma.masked_invalid(dem_data)

    # Get latitude and longitude bounds
    dem_bounds = dem.bounds
    extent = [dem_bounds.left, dem_bounds.right, dem_bounds.bottom, dem_bounds.top]

    # Set normalization for DEM
    norm = Normalize(vmin=np.nanmin(dem_data), vmax=np.nanmax(dem_data))

    # Set NaN values to transparent
    custom_cmap.set_bad(color='white', alpha=0.0)

    # Plot DEM data with custom colormap
    dem_plot = ax_main.imshow(dem_data, origin='upper', cmap=custom_cmap,
                              extent=extent, transform=ccrs.PlateCarree(), alpha=1, zorder=1, norm=norm)

    # Add colorbar
    cbar = plt.colorbar(ScalarMappable(norm=norm, cmap=custom_cmap), 
                        ax=ax_main, orientation='horizontal', fraction=0.075, pad=0.05, label='Elevation (m)')
    cbar.ax.tick_params(direction='in')
    #cbar.ax.set_position([-0.15, 0.1, 0.8, 0.02])  # Adjust the bottom value to move colorbar downward
    

# ===================== Add elevation statistics ========================
# Plot latitude and longitude profiles (same as original code)
# Latitude profile (Right-side subplot)
ax_lat = plt.axes([0.77, 0.275, 0.15, 0.52], sharey=ax_main)  # 调整位置和高度，使副图与主图对齐，避开色带

# 创建纬度数组，与DEM数据行数对应
latitudes = np.linspace(extent[2], extent[3], dem_data.shape[0])

# 确保纬度顺序正确，纬度应从 18°N 到 54°N
if latitudes[0] < latitudes[-1]:
    latitudes = latitudes[::-1]  # 如果顺序是从北到南，则翻转为从南到北

# 如果纬度顺序是从北到南，还需要将 dem_data 按行翻转
if latitudes[0] < latitudes[-1]:
    dem_data = np.flipud(dem_data)  # 上下翻转 DEM 数据，使其纬度顺序从南到北

# 计算纬度方向的平均海拔、最低海拔和最高海拔
elevations_lat = np.nanmean(dem_data, axis=1)  # 计算纬度方向的平均海拔
min_elev_lat = np.nanmin(dem_data, axis=1)     # 计算纬度方向的最低海拔
max_elev_lat = np.nanmax(dem_data, axis=1)     # 计算纬度方向的最高海拔

# 修剪纬度数组和对应的数据，以确保只绘制 18°N 到 54°N 的范围
latitudes_filtered = latitudes[(latitudes >= 18) & (latitudes <= 54)]
elevations_lat_filtered = elevations_lat[(latitudes >= 18) & (latitudes <= 54)]
min_elev_lat_filtered = min_elev_lat[(latitudes >= 18) & (latitudes <= 54)]
max_elev_lat_filtered = max_elev_lat[(latitudes >= 18) & (latitudes <= 54)]

# 绘制纬度-海拔统计图，确保显示有效纬度范围
ax_lat.plot(elevations_lat_filtered, latitudes_filtered, color='blue', label='Mean')  # 绘制平均海拔曲线
ax_lat.fill_betweenx(latitudes_filtered, min_elev_lat_filtered, max_elev_lat_filtered, color='#AFC8E2', alpha=0.75, label='range')  # '#AFC8E2'填充海拔范围

# 设置x轴为海拔，y轴为共享的纬度，并且设置纬度范围为 [18, 54]，与主图对齐
ax_lat.set_xlabel('Elevation (m)')
ax_lat.set_ylim([18, 54])  # 设置与主图相同的纬度范围
ax_lat.grid(True, linestyle='--' , color='#AFC8E2', alpha=0.65)  # #AFC8E2设置虚线的网格线
ax_lat.grid(True)

# 调整x轴范围，确保海拔最大值和最小值完全显示
ax_lat.set_xlim(np.nanmin(min_elev_lat_filtered) - 100, np.nanmax(max_elev_lat_filtered) + 200)
# 如果纬度顺序是反的，确保y轴从南到北（从18°N到54°N）
if latitudes_filtered[0] < latitudes_filtered[-1]:
    ax_lat.invert_yaxis()  # 反转y轴，以便从南到北绘制
# 将纵坐标刻度放在右侧，并设置刻度朝内
ax_lat.yaxis.tick_right()  # 将刻度放置在右侧
ax_lat.tick_params(axis='y', direction='in')  # 刻度朝内
# 设置纬度标签格式为 '°N'
ax_lat.set_yticklabels([f'{int(lat)}°N' for lat in ax_lat.get_yticks()])
# 添加图例
ax_lat.legend(loc='upper right',framealpha=0 ,fontsize=12 )

# Longitude profile (Top-side subplot)
ax_lon = plt.axes([0.15, 0.81, 0.6, 0.15], sharex=ax_main)
longitudes = np.linspace(extent[0], extent[1], dem_data.shape[1])
elevations_lon = np.nanmean(dem_data, axis=0)
min_elev_lon = np.nanmin(dem_data, axis=0)
max_elev_lon = np.nanmax(dem_data, axis=0)

ax_lon.plot(longitudes, elevations_lon, color='blue', label='Mean elevation')
ax_lon.fill_between(longitudes, min_elev_lon, max_elev_lon, color='#AFC8E2', alpha=0.65, label='Elevation range')
ax_lon.set_ylabel('Elevation (m)')
ax_lon.grid(True, linestyle='--', color='#AFC8E2', alpha=0.9)
ax_lon.set_ylim(np.nanmin(min_elev_lon) - 100, np.nanmax(max_elev_lon) + 200)
ax_lon.tick_params(axis='both', direction='in')
ax_lon.xaxis.set_label_position('top')
ax_lon.xaxis.tick_top()
ax_lon.tick_params(labelbottom=False)
ax_lon.set_xticklabels([f'{int(lon)}°E' for lon in ax_lon.get_xticks()])
ax_lon.legend(loc='upper right', framealpha=0, fontsize=12)

# ================== Add map features and site data 添加底图形状==================
# Define colors and transparency for various regions

China_feature = cfeature.ShapelyFeature(shpreader.Reader(China_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2, zorder=2)

Global_countries_feature = cfeature.ShapelyFeature(shpreader.Reader(Global_countries_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='grey', facecolor='none', linewidth=0.5, alpha=0.5, zorder=0)

China_line_feature = cfeature.ShapelyFeature(shpreader.Reader(China_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2, zorder=2)

Mountains_feature = cfeature.ShapelyFeature(shpreader.Reader(mountains_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=0.5, zorder=4)

river_line_feature = cfeature.ShapelyFeature(shpreader.Reader(river_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='blue', facecolor='none', linewidth=0.8, zorder=3)

river_p_feature = cfeature.ShapelyFeature(shpreader.Reader(mountains_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='#ADD8E6', facecolor='none', linewidth=0.8, zorder=3)

ax_main.add_feature(Global_countries_feature)
ax_main.add_feature(China_feature)
ax_main.add_feature(China_line_feature)
ax_main.add_feature(Mountains_feature)
ax_main.add_feature(river_line_feature)
ax_main.add_feature(river_p_feature)

###################################################################################



#################添加山脉三角形形状###################################
# from matplotlib.offsetbox import AnnotationBbox, OffsetImage
# import matplotlib.image as mpimg

# # 加载图片
# mountain_img = mpimg.imread(r'E:\SHP\WGS_1984_SHP\mountains_logo.png')
# assert mountain_img is not None, "图片加载失败，请检查路径是否正确！"

# def plot_line_with_image(ax, geom, img, zoom=0.05):
#     """
#     沿线条绘制自定义图片标记（例如山脉图标）。
#     """
#     if geom.geom_type in ['LineString', 'MultiLineString']:
#         coords = np.array(geom.coords if geom.geom_type == 'LineString' else sum([list(g.coords) for g in geom.geoms], []))
#         for coord in coords[::5]:  # 控制间隔
#             if len(coord) == 2:  # 确保是二维点
#                 x, y = coord
#                 ab = AnnotationBbox(
#                     OffsetImage(img, zoom=zoom), (x, y),
#                     frameon=False, transform=ccrs.PlateCarree(), zorder=10
#                 )
#                 ax.add_artist(ab)

# # 应用到山脉
# for geom in shpreader.Reader(mountains_path).geometries():
#     plot_line_with_image(ax_main, geom, mountain_img, zoom=0.01)
            
def plot_line_with_symbols(ax, geom, symbol='^', color='black', size=6):
    """
    沿线条绘制符号（例如三角形）。
    """
    if geom.geom_type == 'MultiLineString':
        for line in geom.geoms:
            coords = np.array(line.coords)
            ax.scatter(coords[:, 0], coords[:, 1], color=color, s=size, marker=symbol, transform=ccrs.PlateCarree(),zorder=12)
    elif geom.geom_type == 'LineString':
        coords = np.array(geom.coords)
        ax.scatter(coords[:, 0], coords[:, 1], color=color, s=size, marker=symbol, transform=ccrs.PlateCarree(),zorder=12)

# 对所有山脉应用
for geom in shpreader.Reader(mountains_path).geometries():
    plot_line_with_symbols(ax_main, geom, symbol='^', color='black', size=8)
        

#################添加山脉三角形形状###################################


#######添加标注 山脉名称################
# 2. 使用GeoPandas加载shp文件以读取属性信息
gdf = gpd.read_file(mountains_path)
# 打印字段名
print(gdf.columns)

# def calculate_angle(geom):
#     """
#     根据几何对象计算山脉的主方向（旋转角度）。
#     """
#     if geom.geom_type in ['LineString', 'MultiLineString']:
#         # 获取几何的主轴方向
#         line = LineString(geom) if geom.geom_type == 'LineString' else geom
#         coords = np.array(line.coords)  # 获取坐标点
#         if len(coords) < 2:
#             return 0  # 点数不足，返回默认角度0
#         start, end = coords[0], coords[-1]
#     elif geom.geom_type in ['Polygon', 'MultiPolygon']:
#         # 使用多边形的主方向
#         line = LineString(geom.exterior.coords)  # 获取外轮廓线
#         start, end = line.coords[0], line.coords[-1]
#     else:
#         return 0  # 默认角度为0

#     # 计算角度（与水平线的夹角）
#     dx, dy = end[0] - start[0], end[1] - start[1]
    
#     # 计算角度并转换到 0°-180° 范围
#     angle = math.degrees(math.atan2(dy, dx))  # 角度范围 -180° 到 180°
#     #angle = abs(angle)  # 转换到 0°-180° 范围
#     return angle

# def label_near_line(ax, geom, text, offset=1, font_size=4, color='red'):
#     """
#     在线条附近上方标注文本，并偏移一定距离。
    
#     参数:
#     - ax: 地图轴
#     - geom: 线条的几何对象
#     - text: 标注的文本内容
#     - offset: 偏移距离（地理坐标单位，默认为 0.5 度）
#     - font_size: 字体大小
#     - color: 文本颜色
#     """
#     if geom.geom_type == 'MultiLineString':
#         # 选择最长的线条
#         geom = max(geom.geoms, key=lambda line: line.length)
#     elif geom.geom_type != 'LineString':
#         return  # 仅处理 LineString 和 MultiLineString

#     # 获取线条中点及法线方向
#     coords = np.array(geom.coords)
#     if len(coords) < 2:
#         return  # 如果线条点数不足，跳过

#     mid_idx = len(coords) // 2
#     start, end = coords[mid_idx], coords[mid_idx + 1]
#     mid_x, mid_y = (start[0] + end[0]) / 2, (start[1] + end[1]) / 2

#     # 计算法线方向（垂直于线段方向）
#     dx, dy = end[0] - start[0], end[1] - start[1]
#     norm_factor = np.sqrt(dx ** 2 + dy ** 2)
#     if norm_factor == 0:
#         return  # 如果线段长度为 0，跳过
#     normal_x, normal_y = -dy / norm_factor, dx / norm_factor  # 法线方向

#     # 偏移中心点
#     offset_x, offset_y = normal_x * offset, normal_y * offset
#     new_x, new_y = mid_x + offset_x, mid_y + offset_y

#     # 添加标注
#     ax.text(
#         new_x, new_y, text,
#         fontsize=font_size, color=color, ha='center', va='center',
#         transform=ccrs.PlateCarree(), zorder=20, alpha=0.8
#     )

# # 筛选 Shape_Leng > 1.00 的记录
# filtered_gdf = gdf[gdf['Shape_Leng'] > 2.00]

# # 在地图上标注山脉名称并设置旋转角度
# for _, row in filtered_gdf.iterrows():
#     geom = row['geometry']  # 获取几何对象
#     name_en = row['Name_En_1']  # 获取山脉英文名称
#     name_en = name_en.replace("Mountains", "(MT)")


#     # 检查几何类型并计算中心点
#     if geom.geom_type in ['Polygon', 'MultiPolygon']:
#         centroid = geom.centroid
#     elif geom.geom_type in ['LineString', 'MultiLineString']:
#         centroid = geom.centroid
#     elif geom.geom_type == 'Point':
#         centroid = geom
#     else:
#         continue  # 如果是其他几何类型，跳过
        
#     # # 检查几何类型
#     # if geom.geom_type in ['LineString', 'MultiLineString']:
#     #     label_near_line(ax_main, geom, name_en, offset=0.5, font_size=6, color='red')
        
#     # 检查中心点是否在地图范围内
#     if not (73 <= centroid.x <= 135 and 18 <= centroid.y <= 54):
#         continue  # 如果中心点不在范围内，跳过

#     # 计算文本旋转角度
#     rotation_angle = calculate_angle(geom)

#     # 在地图上添加标注
#     ax_main.text(
#         centroid.x, centroid.y, name_en,
#         fontsize=6, color='red', ha='center', va='center',
#         rotation=rotation_angle,  # 设置旋转角度
#         transform=ccrs.PlateCarree(), zorder=20, alpha=0.8
#     )

# #######添加标注 山脉名称###################################################       

# Plot site data
sites_df = gpd.read_file(sites)
#ax_main.scatter(sites_df.geometry.x, sites_df.geometry.y, color='black', s=8, label='Sites', alpha=1, zorder=3)
ax_main.set_extent([73, 135, 18, 54], crs=ccrs.PlateCarree())

# Add gridlines
gl = ax_main.gridlines(draw_labels=False, linewidth=0.3, color='grey', alpha=0.6, linestyle='--')
gl.xlabel_style = {'size':16, 'color': 'black', 'fontname': 'Times New Roman'}
gl.ylabel_style = {'size':16, 'color': 'black', 'fontname': 'Times New Roman'}
gl.top_labels = False
gl.right_labels = False
gl.bottom_labels = True  # Enable labels on the bottom
gl.left_labels = True  # Enable labels on the left

# ======================= Plot South China Sea inset map ========================
ax_inset = plt.axes([0.595, 0.285, 0.2, 0.2], projection=ccrs.PlateCarree())
China_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=1)
China_line_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2)
ax_inset.set_extent([106, 125, 2, 28], crs=ccrs.PlateCarree())
ax_inset.add_feature(China_feature_inset)
ax_inset.add_feature(China_line_feature_inset)
ax_inset.set_xticks([])
ax_inset.set_yticks([])

# ======================= Add legend to the main map ========================
# # 自定义山脉图例（直线加三角形）
mountain_line = mlines.Line2D([], [], color='black', linewidth=1.5, label='Major Mountain Ranges of China')
mountain_marker = mlines.Line2D([], [], color='black', marker='^', markersize=6, linestyle='', label='_nolegend_')  # 三角形不单独显示图例
mountain_patch = (mountain_line, mountain_marker)

# 自定义河流图例（蓝色线条）
river_line = mlines.Line2D([], [], color='blue', linewidth=1.5, label='Primary Rivers of China')

# 添加到图例
ax_main.legend(
    handles=[mountain_patch, river_line],
    labels=['Major Mountain of China', 'Primary Rivers of China'],
    loc='lower left',  # 图例位置，可调整
    fontsize=14,
    bbox_to_anchor=(0.012, 0.098),
    frameon=True,
    fancybox=False, shadow=False,
    #title="Legend",
    #title_fontsize=16,
    framealpha=0,
    edgecolor="black"
)

# 在右上角添加 "(a)" 标注
ax_main.text(0.05, 0.942, '(c)', transform=ax_main.transAxes, fontsize=20, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold')

# ax_main.legend(handles=[arid_patch, semi_arid_patch, humid_patch, semi_humid_patch, semi_humid_area_2_patch],
#                loc='lower left', bbox_to_anchor=(-0.012, -0.032), fancybox=False, shadow=False, fontsize=12, framealpha=0)

# ===============================================================================================

# =====================================# 添加指北针 =========================================================
import matplotlib.patches as mpatches

def add_north(ax, labelsize=18, loc_x=0.95, loc_y=0.99, width=0.06, height=0.09, pad=0.14):
    """
    画一个比例尺带'N'文字注释
    主要参数如下
    :param ax: 要画的坐标区域 Axes实例 plt.gca()获取即可
    :param labelsize: 显示'N'文字的大小
    :param loc_x: 以文字下部为中心的占整个ax横向比例
    :param loc_y: 以文字下部为中心的占整个ax纵向比例
    :param width: 指南针占ax比例宽度
    :param height: 指南针占ax比例高度
    :param pad: 文字符号占ax比例间隙
    :return: None
    """
    minx, maxx = ax.get_xlim()
    miny, maxy = ax.get_ylim()
    ylen = maxy - miny
    xlen = maxx - minx
    left = [minx + xlen*(loc_x - width*.5), miny + ylen*(loc_y - pad)]
    right = [minx + xlen*(loc_x + width*.5), miny + ylen*(loc_y - pad)]
    top = [minx + xlen*loc_x, miny + ylen*(loc_y - pad + height)]
    center = [minx + xlen*loc_x, left[1] + (top[1] - left[1])*.4]
    triangle = mpatches.Polygon([left, top, right, center], color='k')
    ax.text(s='N',
            x=minx + xlen*loc_x,
            y=miny + ylen*(loc_y - pad + height),
            fontsize=labelsize,
            horizontalalignment='center',
            verticalalignment='bottom')
    ax.add_patch(triangle)


# 调用函数，给地图添加新的带箭头的指北针样式
# fig, ax_main = plt.subplots()
add_north(ax_main, labelsize=10, loc_x=0.96, loc_y=0.995, width=0.03, height=0.08, pad=0.14)

# =========================================================================================================

# ======================= 添加比例尺 ========================
from pyproj import Geod
import matplotlib.patches as mpatches

def add_scalebar(ax, length=800, units='km', location=(0.1, 0.02), linewidth=2, color='black', font_color='black'):
    """
    在地图上添加比例尺，长度为真实的地理距离。
    :param ax: matplotlib axes
    :param length: 比例尺的长度（单位：km）
    :param units: 比例尺单位
    :param location: 比例尺放置位置，axes坐标系中的(x, y)坐标
    :param linewidth: 比例尺线条宽度
    :param color: 比例尺颜色
    :param font_color: 文字颜色
    """
    # 获取当前地图的投影信息
    proj = ccrs.PlateCarree()

    # 获取地图左下角和右下角的经纬度（以绘制比例尺）
    extent = ax.get_extent(crs=proj)
    left_lon = extent[0] + (extent[1] - extent[0]) * location[0]  # 左下角经度
    bottom_lat = extent[2] + (extent[3] - extent[2]) * location[1]  # 左下角纬度
    
    # 使用 pyproj.Geod 来计算比例尺的终点经度
    geod = Geod(ellps='WGS84')
    lon2, lat2, _ = geod.fwd(left_lon, bottom_lat, 90, length * 1000)  # 指定长度对应的终点
    
    # 将比例尺在地图上显示出来
    ax.plot([left_lon, lon2], [bottom_lat, bottom_lat], transform=proj, color=color, lw=linewidth, zorder=4)

    # 绘制分段的比例尺
    num_sections = 4
    section_length = length / num_sections
    for i in range(num_sections):
        bar_color = color if i % 2 == 0 else 'white'
        section_lon, section_lat, _ = geod.fwd(left_lon, bottom_lat, 90, section_length * 1000 * i)
        next_section_lon, next_section_lat, _ = geod.fwd(left_lon, bottom_lat, 90, section_length * 1000 * (i + 1))
        ax.add_patch(mpatches.Rectangle(
            (section_lon, bottom_lat), next_section_lon - section_lon, (extent[3] - extent[2]) * 0.01,
            transform=proj, color=bar_color, zorder=4, linewidth=0))

    # 添加比例尺文字
    ax.text((left_lon + lon2) / 2, bottom_lat - (extent[3] - extent[2]) * 0.03,
            f'{length} {units}', ha='center', va='top', fontsize=12, color=font_color,
            transform=proj, zorder=5)


# 调用函数，给地图添加比例尺
add_scalebar(ax_main, length=800, units='km', location=(0.025, 0.09), linewidth=1.5, color='black', font_color='black')


# ======================= Finished plotting ========================
plt.rcParams['axes.unicode_minus'] = False
# 检查文件夹是否存在，如果不存在则创建
output_dir = 'E:\\extreme_snowfall\\China_2169\\中国图\\01研究区示意图\\'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

output_file_base = f'{output_dir}china_mountains'
# 保存 PNG 文件
plt.savefig(f'{output_file_base}.png', format='png', dpi=600, transparent=True, bbox_inches='tight', pad_inches=0.01)
# 保存 SVG 文件
plt.savefig(f'{output_file_base}.svg', format='svg', dpi=600, transparent=True, bbox_inches='tight', pad_inches=0.025)
# 保存 PDF 文件
plt.savefig(f'{output_file_base}.pdf', format='pdf', dpi=600, transparent=True, bbox_inches='tight', pad_inches=0.025)
plt.show()