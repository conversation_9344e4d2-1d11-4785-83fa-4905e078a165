# -*- coding: utf-8 -*-
"""
Read local SHP and add DEM data
1. Plot SHP and South China Sea inset map
2. Plot site data
3. Add DEM data
4. Add elevation statistics for latitude (right side) and longitude (top side)

#配色方案 #E0B77F #BABABC #B7C685 #6A8864 #629641
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as font_manager
import cartopy.crs as ccrs
import cartopy.io.shapereader as shpreader
import cartopy.feature as cfeature
import geopandas as gpd
import matplotlib.patches as mpatches
import rasterio
from rasterio.plot import reshape_as_image
from matplotlib.colors import Normalize, ListedColormap
from matplotlib.cm import ScalarMappable
import numpy as np
from matplotlib.lines import Line2D
import os
import pandas as pd

# Custom function to load .rgb colormap and skip invalid lines
def load_rgb_colormap(file_path):
    colors = []
    with open(file_path, 'r') as file:
        for line in file:
            # Skip lines that are comments or non-RGB data (e.g., "ncolors")
            if line.startswith('#') or not line.strip():
                continue  # Skip comments or empty lines
            
            # Split the line into components
            try:
                rgb = [int(x) for x in line.strip().split()]
                if len(rgb) == 3:  # Ensure it's a valid RGB line
                    colors.append([rgb[0]/255, rgb[1]/255, rgb[2]/255])  # Normalize to [0, 1] range
            except ValueError:
                # Skip lines that can't be converted to integers
                continue

    return ListedColormap(colors)


# Set local data paths
fig = plt.figure(figsize=(12, 8), dpi=600)

Global_countries_path = r'E:/SHP/WGS_1984_SHP/global_countries.shp'
China_path = r'E:/SHP/WGS_1984_SHP/China.shp'
China_line_path = r'E:/SHP/WGS_1984_SHP/合并_中国_省_MultiLineString.shp'
arid_area = r'E:/SHP/WGS_1984_SHP/合并后的干旱区.shp'
semi_arid_area = r'E:/SHP/WGS_1984_SHP/合并后的半干旱区.shp'
semi_humid_area = r'E:/SHP/WGS_1984_SHP/合并后的半湿润地区.shp'
humid_area = r'E:/SHP/WGS_1984_SHP/合并后的湿润地区.shp'
semi_humid_area_2 = r'E:/SHP/WGS_1984_SHP/湿润_半湿润地区.shp'
sites = r'E:/SHP/WGS_1984_SHP/China_point.shp'
dem_path = r'E:/SHP/China_dem_reprojected.tif'

# Set global font to Times New Roman, font size to 12
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 20

# Load custom colormap
cmap_path = r'E:/色带/python色带/cmaps-master/cmaps-master/cmaps/colormaps/ncar_ncl/cmp_b2r.rgb'  
#cmp_b2r.rgb
#amwg256.rgb #BlueRed.rgb #Cat12.rgb #hotcolr_19lev.rgb #matlab_hot.rgb #MPL_afmhot.rgb #MPL_RdPu.rgb 
#BlAqGrYeOrReVi200.rgb #BlueWhiteOrangeRed.rgb #cb_rainbow.rgb #CBR_wet.rgb #amwg.rgb
custom_cmap = load_rgb_colormap(cmap_path)

# Create a map with PlateCarree projection (main map)
ax_main = plt.axes([0.15, 0.2, 0.6, 0.6], projection=ccrs.PlateCarree())  # Adjust main map position

# # ======================= Read and plot DEM data ========================
# with rasterio.open(dem_path) as dem:
#     dem_data = dem.read(1)
#     dem_data = dem_data.astype(float)
#     dem_data[dem_data == -32766] = np.nan
#     dem_data[dem_data < -500] = np.nan
#     dem_data = np.ma.masked_invalid(dem_data)

#     # Get latitude and longitude bounds
#     dem_bounds = dem.bounds
#     extent = [dem_bounds.left, dem_bounds.right, dem_bounds.bottom, dem_bounds.top]

#     # Set normalization for DEM
#     norm = Normalize(vmin=np.nanmin(dem_data), vmax=np.nanmax(dem_data))

#     # Set NaN values to transparent
#     custom_cmap.set_bad(color='white', alpha=0.0)

#     # Plot DEM data with custom colormap
#     dem_plot = ax_main.imshow(dem_data, origin='upper', cmap=custom_cmap,
#                               extent=extent, transform=ccrs.PlateCarree(), alpha=0.95, zorder=0, norm=norm)

#     # Add colorbar
#     cbar = plt.colorbar(ScalarMappable(norm=norm, cmap=custom_cmap), 
#                         ax=ax_main, orientation='horizontal', fraction=0.075, pad=0.05, label='Elevation (m)')
#     cbar.ax.tick_params(direction='in')

# # ===================== Add elevation statistics ========================
# # Plot latitude and longitude profiles (same as original code)
# # Latitude profile (Right-side subplot)
# ax_lat = plt.axes([0.77, 0.275, 0.15, 0.52], sharey=ax_main)  # 调整位置和高度，使副图与主图对齐，避开色带

# # 创建纬度数组，与DEM数据行数对应
# latitudes = np.linspace(extent[2], extent[3], dem_data.shape[0])

# # 确保纬度顺序正确，纬度应从 18°N 到 54°N
# if latitudes[0] < latitudes[-1]:
#     latitudes = latitudes[::-1]  # 如果顺序是从北到南，则翻转为从南到北

# # 如果纬度顺序是从北到南，还需要将 dem_data 按行翻转
# if latitudes[0] < latitudes[-1]:
#     dem_data = np.flipud(dem_data)  # 上下翻转 DEM 数据，使其纬度顺序从南到北

# # 计算纬度方向的平均海拔、最低海拔和最高海拔
# elevations_lat = np.nanmean(dem_data, axis=1)  # 计算纬度方向的平均海拔
# min_elev_lat = np.nanmin(dem_data, axis=1)     # 计算纬度方向的最低海拔
# max_elev_lat = np.nanmax(dem_data, axis=1)     # 计算纬度方向的最高海拔

# # 修剪纬度数组和对应的数据，以确保只绘制 18°N 到 54°N 的范围
# latitudes_filtered = latitudes[(latitudes >= 18) & (latitudes <= 54)]
# elevations_lat_filtered = elevations_lat[(latitudes >= 18) & (latitudes <= 54)]
# min_elev_lat_filtered = min_elev_lat[(latitudes >= 18) & (latitudes <= 54)]
# max_elev_lat_filtered = max_elev_lat[(latitudes >= 18) & (latitudes <= 54)]

# # 绘制纬度-海拔统计图，确保显示有效纬度范围
# ax_lat.plot(elevations_lat_filtered, latitudes_filtered, color='blue', label='Mean')  # 绘制平均海拔曲线
# ax_lat.fill_betweenx(latitudes_filtered, min_elev_lat_filtered, max_elev_lat_filtered, color='#AFC8E2', alpha=0.65, label='range')  # '#AFC8E2'填充海拔范围

# # 设置x轴为海拔，y轴为共享的纬度，并且设置纬度范围为 [18, 54]，与主图对齐
# ax_lat.set_xlabel('Elevation (m)')
# ax_lat.set_ylim([18, 54])  # 设置与主图相同的纬度范围
# ax_lat.grid(True, linestyle='--' , color='#AFC8E2', alpha=0.65)  # #AFC8E2设置虚线的网格线
# ax_lat.grid(True)

# # 调整x轴范围，确保海拔最大值和最小值完全显示
# ax_lat.set_xlim(np.nanmin(min_elev_lat_filtered) - 100, np.nanmax(max_elev_lat_filtered) + 200)

# # 如果纬度顺序是反的，确保y轴从南到北（从18°N到54°N）
# if latitudes_filtered[0] < latitudes_filtered[-1]:
#     ax_lat.invert_yaxis()  # 反转y轴，以便从南到北绘制

# # 将纵坐标刻度放在右侧，并设置刻度朝内
# ax_lat.yaxis.tick_right()  # 将刻度放置在右侧
# ax_lat.tick_params(axis='y', direction='in')  # 刻度朝内

# # 设置纬度标签格式为 '°N'
# ax_lat.set_yticklabels([f'{int(lat)}°N' for lat in ax_lat.get_yticks()])

# 添加图例
#ax_lat.legend(loc='upper right',framealpha=0)

# # Longitude profile (Top-side subplot)
# ax_lon = plt.axes([0.15, 0.81, 0.6, 0.15], sharex=ax_main)
# longitudes = np.linspace(extent[0], extent[1], dem_data.shape[1])
# elevations_lon = np.nanmean(dem_data, axis=0)
# min_elev_lon = np.nanmin(dem_data, axis=0)
# max_elev_lon = np.nanmax(dem_data, axis=0)

#ax_lon.plot(longitudes, elevations_lon, color='blue', label='Mean elevation')
#ax_lon.fill_between(longitudes, min_elev_lon, max_elev_lon, color='#AFC8E2', alpha=0.65, label='Elevation range')
#ax_lon.set_ylabel('Elevation (m)')
# ax_lon.grid(True, linestyle='--', color='#AFC8E2', alpha=0.9)
# ax_lon.set_ylim(np.nanmin(min_elev_lon) - 100, np.nanmax(max_elev_lon) + 200)
# ax_lon.tick_params(axis='both', direction='in')
# ax_lon.xaxis.set_label_position('top')
# ax_lon.xaxis.tick_top()
# ax_lon.tick_params(labelbottom=False)
# ax_lon.set_xticklabels([f'{int(lon)}°E' for lon in ax_lon.get_xticks()])
# ax_lon.legend(loc='upper right', framealpha=0)

# ================== Add map features and site data ==================
# Define colors and transparency for various regions

China_feature = cfeature.ShapelyFeature(gpd.read_file(China_path).geometry, ccrs.PlateCarree(), 
                                        edgecolor='black', facecolor='none', linewidth=2, zorder=2)
Global_countries_feature = cfeature.ShapelyFeature(gpd.read_file(Global_countries_path).geometry, ccrs.PlateCarree(), 
                                                   edgecolor='grey', facecolor='none', linewidth=0.5, alpha=0.3, zorder=1)
China_line_feature = cfeature.ShapelyFeature(gpd.read_file(China_line_path).geometry, ccrs.PlateCarree(),  
                                             edgecolor='black', facecolor='none', linewidth=2, zorder=2)
arid_feature = cfeature.ShapelyFeature(gpd.read_file(arid_area).geometry, ccrs.PlateCarree(), 
                                       edgecolor='black', facecolor='#696969', linewidth=1, alpha=0.85, zorder=2)  #E0B77F,#4682B4
semi_arid_feature = cfeature.ShapelyFeature(gpd.read_file(semi_arid_area).geometry, ccrs.PlateCarree(), 
                                            edgecolor='black', facecolor='#A9A9A9', linewidth=1, alpha=0.85, zorder=2) #BABABC,#6A9FB5
humid_feature = cfeature.ShapelyFeature(gpd.read_file(humid_area).geometry, ccrs.PlateCarree(), 
                                        edgecolor='black', facecolor='#629641', linewidth=1, alpha=0.85, zorder=2)
semi_humid_feature = cfeature.ShapelyFeature(gpd.read_file(semi_humid_area).geometry, ccrs.PlateCarree(), 
                                             edgecolor='black', facecolor='#6A8864', linewidth=1, alpha=0.85, zorder=2)
semi_humid_area_2_feature = cfeature.ShapelyFeature(gpd.read_file(semi_humid_area_2).geometry, ccrs.PlateCarree(), 
                                                    edgecolor='black', facecolor='#B7C685', linewidth=1, alpha=0.85, zorder=2)

ax_main.add_feature(arid_feature)
ax_main.add_feature(semi_arid_feature)
ax_main.add_feature(humid_feature)
ax_main.add_feature(semi_humid_feature)
ax_main.add_feature(semi_humid_area_2_feature)
ax_main.add_feature(Global_countries_feature)
ax_main.add_feature(China_feature)
ax_main.add_feature(China_line_feature)

# Plot site data
sites_df = gpd.read_file(sites)
ax_main.scatter(sites_df.geometry.x, sites_df.geometry.y, color='Purple', s=6, label='Sites', alpha=0.75, zorder=3)
ax_main.set_extent([73, 135, 16, 56], crs=ccrs.PlateCarree())


# Add gridlines with latitude and longitude labels
gl = ax_main.gridlines(draw_labels=True, linewidth=0.5, color='grey', alpha=0.36, linestyle='--')

# Customize label styles
gl.xlabel_style = {'size': 20, 'color': 'black', 'fontname': 'Times New Roman'}
gl.ylabel_style = {'size': 20, 'color': 'black', 'fontname': 'Times New Roman'}

# Enable or disable specific sides of the labels
gl.top_labels = False  # Disable labels on the top
gl.right_labels = False  # Disable labels on the right
gl.bottom_labels = True  # Enable labels on the bottom
gl.left_labels = True  # Enable labels on the left

# ======================= Plot South China Sea inset map ========================
ax_inset = plt.axes([0.596, 0.216, 0.2, 0.2], projection=ccrs.PlateCarree())
China_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_path).geometries(),
                                              ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=1)
China_line_feature_inset = cfeature.ShapelyFeature(shpreader.Reader(China_line_path).geometries(),
                                                   ccrs.PlateCarree(), edgecolor='black', facecolor='none', linewidth=2)
ax_inset.set_extent([106, 125, 2, 28], crs=ccrs.PlateCarree())
ax_inset.add_feature(China_feature_inset)
ax_inset.add_feature(China_line_feature_inset)
ax_inset.set_xticks([])
ax_inset.set_yticks([])
#配色方案 #E0B77F #BABABC #B7C685 #6A8864 #629641 
#'#E0B77F','#BABABC','#629641','#6A8864','#B7C685',
# ======================= Add legend to the main map ========================
arid_patch = mpatches.Patch(edgecolor='black', facecolor='#696969', label='Arid Zone', linewidth=1, alpha=0.7)
semi_arid_patch = mpatches.Patch(edgecolor='black', facecolor='#A9A9A9', label='Semi-Arid Zone', linewidth=1, alpha=0.7)
humid_patch = mpatches.Patch(edgecolor='black', facecolor='#629641', label='Humid Zone', linewidth=1, alpha=0.7)
semi_humid_patch = mpatches.Patch(edgecolor='black', facecolor='#6A8864', label='Semi-Humid Zone', linewidth=1, alpha=0.7)
semi_humid_area_2_patch = mpatches.Patch(edgecolor='black', facecolor='#B7C685', label='Humid/Semi-Humid Zone', linewidth=1, alpha=0.7)
site_legend = Line2D([0], [0], marker='o', color='Purple', label='Meteorological Stations', markersize=6, linestyle='None')

# Adjusting the legend to make it more compact
ax_main.legend(
    handles=[arid_patch, semi_arid_patch, humid_patch, semi_humid_patch, semi_humid_area_2_patch, site_legend],
    loc='lower left',  # Adjust this value depending on where you want the legend
    bbox_to_anchor=(-0.002, -0.022),  # Adjust this to move the legend around
    fancybox=False,  # Keep box corners sharp to avoid extra space
    shadow=False,  # Disable shadow for a cleaner look
    fontsize=13.2,  # Slightly smaller font size for compactness
    framealpha=0,  # Make the frame fully opaque
    borderpad=0.2,  # Reduce padding within the legend box
    labelspacing=0.2,  # Reduce space between labels
    handlelength=2,  # Make the handles shorter
    handleheight=1.5,  # Reduce height of legend markers (patches)
    borderaxespad=0.5  # Reduce space between legend and axes
)

# 在右上角添加 "(a)" 标注
# Add text annotations before plt.show()
ax_main.text(0.05, 0.942, '(b)', transform=ax_main.transAxes, fontsize=20, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', zorder=10, clip_on=False)

ax_main.text(0.90, 0.742, 'HZ-Ⅰ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', 
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.62, 0.342, 'HZ-Ⅱ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', 
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.68, 0.48, 'SHZ-Ⅰ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', 
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.38, 0.43, 'SHZ-Ⅱ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', 
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.28, 0.62, 'AZ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', 
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.48, 0.50, 'SAZ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', rotation=0,
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))

ax_main.text(0.42, 0.35, 'HSHZ', transform=ax_main.transAxes, fontsize=16, color='black', 
             ha='center', va='center', fontname='Times New Roman', fontweight='bold', rotation=0,
             zorder=10, clip_on=False, bbox=dict(facecolor=(1, 1, 1, 0.5), edgecolor='none', pad=2))
# ======================= Finished plotting ========================

# ======================= Plot bar chart for station counts ========================
# Calculate number of stations in each region
# 定义缩写映射关系
short_names = {
    'Arid': 'AZ',
    'Semi-Arid': 'SAZ',
    'Humid': 'HZ',
    'Semi-Humid': 'SHZ',
    'Humid/Semi-Humid': 'HSHZ'
}

# 定义计算站点数量的函数
def count_stations_in_region(stations_df, region_shapefile):
    # 读取区域 shapefile 并确保坐标系一致
    region_gdf = gpd.read_file(region_shapefile)
    
    # 检查并匹配坐标系，如果不同则统一为 EPSG:4326
    if stations_df.crs != region_gdf.crs:
        stations_df = stations_df.to_crs(region_gdf.crs)
    
    # 使用 unary_union 合并区域多边形，确保判断时没有分割的几何
    merged_region = region_gdf.unary_union
    
    # 使用 within() 判断站点是否在区域内
    return stations_df.within(merged_region).sum()

# 站点数据
sites_df = gpd.read_file(sites)

# 计算站点数量并应用缩写
station_counts = {
    short_names['Arid']: count_stations_in_region(sites_df, arid_area),
    short_names['Semi-Arid']: count_stations_in_region(sites_df, semi_arid_area),
    short_names['Humid']: count_stations_in_region(sites_df, humid_area),
    short_names['Semi-Humid']: count_stations_in_region(sites_df, semi_humid_area),
    short_names['Humid/Semi-Humid']: count_stations_in_region(sites_df, semi_humid_area_2)
}

# 将 station_counts 转换为 pandas Series
station_counts_series = pd.Series(station_counts)

# 打印站点数量，检查是否计算正确
print(station_counts_series)

# Create a bar chart in the top right corner
ax_bar = plt.axes([0.342, 0.668, 0.18, 0.12])  # Adjust position and size of the bar chart
bars = ax_bar.bar(station_counts_series.index, station_counts_series.values, 
                  color=['#696969', '#A9A9A9', '#629641', '#6A8864', '#B7C685'])

# 设置字体大小小一些
ax_bar.tick_params(axis='x', labelsize=14)
ax_bar.tick_params(axis='y', labelsize=14)

# 将纵坐标刻度移动到右边
ax_bar.yaxis.tick_right()

# 添加每个柱状图上方的站点数量
for bar in bars:
    height = bar.get_height()
    ax_bar.text(bar.get_x() + bar.get_width()/2, height, f'{int(height)}', 
                ha='center', va='bottom', fontsize=14)

# Add labels and grid
# ax_bar.set_ylabel('Number of Stations', fontsize=14)

# Adjust y-axis limits
ax_bar.set_ylim(station_counts_series.min(), station_counts_series.max() + 200)  
ax_bar.set_xticks(range(len(station_counts_series)))
ax_bar.set_xticklabels(station_counts_series.index, rotation=25, ha='center', fontsize=14)
ax_bar.grid(True, linestyle='--', alpha=0.35)

# ======================= Finished plotting ========================
plt.rcParams['axes.unicode_minus'] = False

# 检查文件夹是否存在，如果不存在则创建
output_dir = 'E:\\extreme_snowfall\\China_2169\\中国图\\01研究区示意图\\'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

output_file_base = f'{output_dir}china_subdivisions1'

# 保存 PNG 文件
plt.savefig(f'{output_file_base}.png', format='png', dpi=500, transparent=True, bbox_inches='tight', pad_inches=0.01)
# 保存 SVG 文件
plt.savefig(f'{output_file_base}.svg', format='svg', dpi=500, transparent=True, bbox_inches='tight', pad_inches=0.025)
# 保存 PDF 文件
plt.savefig(f'{output_file_base}.pdf', format='pdf', dpi=500, transparent=True, bbox_inches='tight', pad_inches=0.025)

# 显示图像
plt.show()