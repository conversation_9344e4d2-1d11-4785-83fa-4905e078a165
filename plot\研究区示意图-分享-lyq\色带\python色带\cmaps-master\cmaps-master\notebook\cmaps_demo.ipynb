{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x11a682940>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "import cmaps\n", "import numpy as np\n", "\n", "x = y = np.arange(-3.0, 3.01, 0.05)\n", "X, Y = np.meshgrid(x, y)\n", "\n", "sigmax = sigmay = 1.0\n", "mux = muy = sigmaxy=0.0\n", "    \n", "Xmu = X-mux\n", "Ymu = Y-muy\n", "\n", "rho = sigmaxy/(sigmax*sigmay)\n", "z = Xmu**2/sigmax**2 + Ymu**2/sigmay**2 - 2*rho*Xmu*Ymu/(sigmax*sigmay)\n", "denom = 2*np.pi*sigmax*sigmay*np.sqrt(1-rho**2)\n", "Z = np.exp(-z/(2*(1-rho**2))) / denom\n", "\n", "plt.pcolormesh(X,Y,Z,cmap=cmaps.BkBlAqGrYeOrReViWh200)\n", "plt.colorbar()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 1}